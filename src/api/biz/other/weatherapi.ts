import axios from 'axios'

// 高德地图天气API接口
export interface WeatherData {
	temperature: number
	weather: string
	city: string
	humidity?: string
	windDirection?: string
	windSpeed?: string
}

// 高德地图天气API响应结构
export interface AmapWeatherResponse {
	status: string
	count: string
	info: string
	infocode: string
	lives: Array<{
		province: string
		city: string
		adcode: string
		weather: string
		temperature: string
		winddirection: string
		windpower: string
		humidity: string
		reporttime: string
		temperature_float: string
		humidity_float: string
	}>
}

export const BIZ_WEATHER_APIS = {
	/**
	 * 获取天气信息 - 使用高德地图API（直接使用axios避免CORS问题）
	 * @returns Promise<WeatherData>
	 */
	getFormattedWeather: async (): Promise<WeatherData> => {
		try {
			// 直接使用axios请求，避免xRequest添加的authorization头导致CORS问题
			const response = await axios.get<AmapWeatherResponse>('https://restapi.amap.com/v3/weather/weatherInfo', {
				params: {
					key: '01cf92dc25048c1971ffb455e46f381e',
					city: '140100', // 山西太原
					extensions: 'base',
				},
				timeout: 5000, // 5秒超时
			})

			const data = response.data

			if (data.status !== '1' || !data.lives || data.lives.length === 0) {
				throw new Error('获取天气数据失败')
			}

			const weatherInfo = data.lives[0]

			return {
				temperature: parseInt(weatherInfo.temperature),
				weather: weatherInfo.weather,
				city: weatherInfo.city,
				humidity: weatherInfo.humidity + '%',
				windDirection: weatherInfo.winddirection,
				windSpeed: weatherInfo.windpower,
			}
		} catch (error) {
			console.error('获取天气信息失败:', error)
			// 返回默认数据
			return {
				temperature: 24,
				weather: '晴',
				city: '太原',
				humidity: '45%',
				windDirection: '东南',
				windSpeed: '≤3',
			}
		}
	},

	/**
	 * 根据IP获取天气信息（简化版，直接使用固定城市）
	 * @returns Promise<WeatherData>
	 */
	getWeatherByIP: async (): Promise<WeatherData> => {
		return BIZ_WEATHER_APIS.getFormattedWeather()
	},

	/**
	 * 兼容旧版本的getWeather方法
	 * @param params 请求参数（已忽略）
	 * @returns Promise<WeatherData>
	 */
	getWeather: async (params?: any): Promise<WeatherData> => {
		return BIZ_WEATHER_APIS.getFormattedWeather()
	},
}
